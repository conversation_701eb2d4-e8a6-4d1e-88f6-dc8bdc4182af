import { useEffect, useState } from "react";
import { Textbox, Rect, Line, Circle, Polygon } from "fabric";
import { toggleDrawingMode, enableDrawing, type ToolMode } from "@/lib/fabric";
import {
  FaTrash,
  FaSave,
  FaPen,
  FaSun,
  FaAdjust,
  FaFont,
  FaPalette,
  FaExchangeAlt,
  FaSquare,
  FaMinus,
  FaCircle,
  FaDrawPolygon,
  FaRedo,
  FaArrowsAltH,
  FaArrowsAltV,
  FaMousePointer,
  FaSearchPlus,
  FaGem,
  FaUndo,
} from "react-icons/fa";
import { FabricToolbarProps } from "@/shared/types";

const FabricToolbar: React.FC<FabricToolbarProps> = ({
  fabricCanvas,
  brightness,
  contrast,
  grayscale,
  invert,
  sharpness,
  gammaR,
  gammaG,
  gammaB,
  onBrightnessChange,
  onContrastChange,
  onGrayscaleChange,
  onInvertChange,
  onSharpnessChange,
  onGammaRChange,
  onGammaGChange,
  onGammaBChange,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onUndo,
  canUndo = false,
  onSave,
  onShapeCreated,
  disableGrayscale = false,
  disableGamma = false,
}) => {
  const [activeMode, setActiveMode] = useState<ToolMode>("select");

  const tools = [
    {
      icon: FaMousePointer,
      title: "Select/Move objects",
      mode: "select" as ToolMode,
    },
    { icon: FaPen, title: "Draw freehand paths", mode: "freehand" as ToolMode },
    { icon: FaFont, title: "Add text", mode: "text" as ToolMode },
    { icon: FaSquare, title: "Add rectangle", mode: "rect" as ToolMode },
    { icon: FaMinus, title: "Add line", mode: "line" as ToolMode },
    { icon: FaCircle, title: "Add circle", mode: "circle" as ToolMode },
    { icon: FaDrawPolygon, title: "Add triangle", mode: "polygon" as ToolMode },
  ];

  const sliders = [
    {
      icon: FaSun,
      label: "Bright",
      value: brightness,
      onChange: onBrightnessChange,
      min: 0.1,
      max: 2,
      step: 0.1,
    },
    {
      icon: FaAdjust,
      label: "Contrast",
      value: contrast,
      onChange: onContrastChange,
      min: 0.1,
      max: 2,
      step: 0.1,
    },
    {
      icon: FaSearchPlus,
      label: "Sharpness",
      value: sharpness,
      onChange: onSharpnessChange,
      min: 0.1,
      max: 2,
      step: 0.1,
    },
  ];

  const gammaSliders = [
    {
      label: "R",
      value: gammaR,
      onChange: onGammaRChange,
      className: "gamma-red",
    },
    {
      label: "G",
      value: gammaG,
      onChange: onGammaGChange,
      className: "gamma-green",
    },
    {
      label: "B",
      value: gammaB,
      onChange: onGammaBChange,
      className: "gamma-blue",
    },
  ];

  const createShape = (mode: ToolMode, pointer: { x: number; y: number }) => {
    if (!fabricCanvas.current) return;

    const canvas = fabricCanvas.current;
    let shape;

    switch (mode) {
      case "text":
        shape = new Textbox("Text", {
          left: pointer.x,
          top: pointer.y,
          fontSize: 20,
          fill: "red",
          fontFamily: "Arial",
          width: 200,
        });
        canvas.add(shape);
        canvas.setActiveObject(shape);
        (shape as Textbox).enterEditing();
        if (onShapeCreated) onShapeCreated();
        break;
      case "rect":
        shape = new Rect({
          left: pointer.x,
          top: pointer.y,
          width: 100,
          height: 60,
          fill: "transparent",
          stroke: "red",
          strokeWidth: 2,
        });
        canvas.add(shape);
        canvas.setActiveObject(shape);
        if (onShapeCreated) onShapeCreated();
        break;
      case "line":
        shape = new Line([pointer.x, pointer.y, pointer.x + 100, pointer.y], {
          stroke: "red",
          strokeWidth: 2,
        });
        canvas.add(shape);
        canvas.setActiveObject(shape);
        if (onShapeCreated) onShapeCreated();
        break;
      case "circle":
        shape = new Circle({
          left: pointer.x,
          top: pointer.y,
          radius: 50,
          fill: "transparent",
          stroke: "red",
          strokeWidth: 2,
        });
        canvas.add(shape);
        canvas.setActiveObject(shape);
        if (onShapeCreated) onShapeCreated();
        break;
      case "polygon":
        shape = new Polygon(
          [
            { x: pointer.x, y: pointer.y },
            { x: pointer.x + 50, y: pointer.y + 80 },
            { x: pointer.x - 50, y: pointer.y + 80 },
          ],
          {
            fill: "transparent",
            stroke: "red",
            strokeWidth: 2,
          }
        );
        canvas.add(shape);
        canvas.setActiveObject(shape);
        if (onShapeCreated) onShapeCreated();
        break;
    }

    canvas.renderAll();
  };

  useEffect(() => {
    if (!fabricCanvas.current) return;

    const canvas = fabricCanvas.current;

    const mouseDownHandler = (e: any) => {
      if (activeMode === "freehand") {
        enableDrawing(canvas);
      } else if (activeMode !== "select" && e.pointer) {
        createShape(activeMode, e.pointer);
        setActiveMode("select");
      }
    };

    const mouseUpHandler = () => {
      if (activeMode !== "freehand") {
        canvas.isDrawingMode = false;
      }
    };

    const disposeMouseDown = canvas.on("mouse:down", mouseDownHandler);
    const disposeMouseUp = canvas.on("mouse:up", mouseUpHandler);

    return () => {
      disposeMouseDown();
      disposeMouseUp();
    };
  }, [fabricCanvas.current, activeMode, createShape, enableDrawing]);

  useEffect(() => {
    if (!fabricCanvas.current) return;
    toggleDrawingMode(fabricCanvas.current, activeMode === "freehand");
  }, [activeMode, fabricCanvas.current]);

  useEffect(() => {
    if (!fabricCanvas.current) return;
    if (activeMode !== "select" && activeMode !== "freehand") {
      fabricCanvas.current.defaultCursor = "crosshair";
    } else {
      fabricCanvas.current.defaultCursor = "default";
    }
  }, [activeMode, fabricCanvas.current]);

  const handleSave = () => {
    if (onSave) onSave();
  };

  const handleClear = () => {
    if (!fabricCanvas.current) return;

    const canvas = fabricCanvas.current;
    const backgroundImage = canvas.backgroundImage;

    // Clear all objects without triggering events
    canvas.clear();

    // Restore the background image if it was set
    if (backgroundImage) {
      canvas.backgroundImage = backgroundImage;
    }

    canvas.renderAll();
  };

  return (
    <div className="fabric-toolbar-vertical">
      <div className="annotation-tools">
        <div className="tool-grid">
          {tools.map((tool) => {
            const IconComponent = tool.icon;
            return (
              <button
                key={tool.mode}
                className={`tool-btn ${
                  activeMode === tool.mode ? "active" : ""
                }`}
                onClick={() => setActiveMode(tool.mode)}
                title={tool.title}
              >
                <IconComponent />
              </button>
            );
          })}

          <button
            className="tool-btn"
            onClick={onRotate}
            title="Rotate selected object"
          >
            <FaRedo />
          </button>
          <button
            className="tool-btn"
            onClick={onFlipHorizontal}
            title="Flip horizontal"
          >
            <FaArrowsAltH />
          </button>
          <button
            className="tool-btn"
            onClick={onFlipVertical}
            title="Flip vertical"
          >
            <FaArrowsAltV />
          </button>
          <button
            className={`tool-btn ${grayscale ? "active" : ""} ${
              disableGrayscale ? "disabled" : ""
            }`}
            onClick={() => !disableGrayscale && onGrayscaleChange(!grayscale)}
            disabled={disableGrayscale}
            title="Toggle grayscale"
          >
            <FaPalette />
          </button>
          <button
            className={`tool-btn ${invert ? "active" : ""}`}
            onClick={() => onInvertChange(!invert)}
            title="Toggle invert"
          >
            <FaExchangeAlt />
          </button>
        </div>

        <div className="utility-tools">
          <button className="clear-btn" onClick={handleClear} title="Clear All">
            <FaTrash />
          </button>
        </div>
      </div>

      {sliders.map((slider) => {
        const IconComponent = slider.icon;
        return (
          <div key={slider.label} className="control-item">
            <div className="control-icon" title={slider.label}>
              <IconComponent />
            </div>
            <input
              type="range"
              min={slider.min}
              max={slider.max}
              step={slider.step}
              value={slider.value}
              onChange={(e) => slider.onChange(parseFloat(e.target.value))}
              className="horizontal-slider"
              title={`${slider.label}: ${slider.value.toFixed(1)}`}
            />
            <span>{slider.value.toFixed(1)}</span>
          </div>
        );
      })}

      <div className={`gamma-controls ${disableGamma ? "disabled" : ""}`}>
        <div className="gamma-icon" title="Gamma RGB">
          <FaGem />
        </div>
        <div className="gamma-sliders">
          {gammaSliders.map((gamma) => (
            <div key={gamma.label} className="gamma-slider-item">
              <div className={`gamma-label ${gamma.label.toLowerCase()}`}>
                {gamma.label}
              </div>
              <input
                type="range"
                min="1"
                max="2.2"
                step="0.05"
                value={gamma.value}
                onChange={(e) =>
                  !disableGamma && gamma.onChange(parseFloat(e.target.value))
                }
                className={`horizontal-slider ${gamma.className}`}
                disabled={disableGamma}
                title={`Gamma ${gamma.label}: ${gamma.value.toFixed(2)}`}
              />
              <span className="gamma-value">{gamma.value.toFixed(2)}</span>
            </div>
          ))}
        </div>
      </div>

      {onUndo && (
        <button
          className={`save-btn undo-btn ${!canUndo ? "disabled" : ""}`}
          onClick={canUndo ? onUndo : undefined}
          disabled={!canUndo}
          title="Undo"
        >
          <FaUndo />
        </button>
      )}

      <button className="save-btn" onClick={handleSave} title="Save">
        <FaSave />
      </button>
    </div>
  );
};

export default FabricToolbar;
