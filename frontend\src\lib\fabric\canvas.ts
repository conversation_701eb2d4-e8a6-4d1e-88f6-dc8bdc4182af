import { Canvas, PencilBrush, FabricImage, filters } from "fabric";

export interface CanvasConfig {
  readonly selection: boolean;
  readonly backgroundColor: string;
}

export interface BrushConfig {
  readonly color: string;
  readonly width: number;
}

export interface FilterParams {
  brightness: number;
  contrast: number;
  grayscale: boolean;
  invert: boolean;
  sharpness: number;
  gammaR: number;
  gammaG: number;
  gammaB: number;
}

export type PartialFilterParams = Partial<FilterParams>;

const CANVAS_CONFIG: CanvasConfig = {
  selection: true,
  backgroundColor: "transparent",
} as const;

const BRUSH_CONFIG: BrushConfig = {
  color: "red",
  width: 3,
} as const;

export const enableDrawing = (canvas: Canvas): void => {
  canvas.freeDrawingBrush = new PencilBrush(canvas);
  canvas.freeDrawingBrush.color = BRUSH_CONFIG.color;
  canvas.freeDrawingBrush.width = BRUSH_CONFIG.width;
};

export const toggleDrawingMode = (canvas: Canvas, enabled: boolean): void => {
  canvas.isDrawingMode = enabled;
  if (enabled) enableDrawing(canvas);
  canvas.renderAll();
};

export const loadAnnotations = async (
  canvas: Canvas,
  annotations: unknown
): Promise<void> => {
  if (!annotations || !canvas) return;

  const backgroundImage = canvas.backgroundImage;

  // Convert image-relative coordinates to absolute coordinates
  const absoluteAnnotations = convertImageRelativeToAbsolute(annotations, backgroundImage);

  await canvas.loadFromJSON(absoluteAnnotations);

  if (backgroundImage) {
    canvas.backgroundImage = backgroundImage;
  }

  canvas.calcOffset();
  canvas.renderAll();
};

/**
 * Convert image-relative coordinates back to absolute coordinates
 */
const convertImageRelativeToAbsolute = (annotations: any, backgroundImage: any) => {
  if (!annotations || !backgroundImage || !annotations.objects) {
    return annotations;
  }

  const currentImageWidth = backgroundImage.width;
  const currentImageHeight = backgroundImage.height;

  // Convert each annotation from image-relative to absolute coordinates
  const convertedAnnotations = {
    ...annotations,
    objects: annotations.objects.map((obj: any) => {
      if (obj._imageRelative) {
        // Use stored reference dimensions if available, otherwise use current image dimensions
        const referenceWidth = obj._referenceWidth || currentImageWidth;
        const referenceHeight = obj._referenceHeight || currentImageHeight;

        const absolute = {
          ...obj,
          left: obj.left * referenceWidth,      // Convert using reference dimensions
          top: obj.top * referenceHeight,       // Convert using reference dimensions
          _imageRelative: undefined,            // Remove the marker
          _referenceWidth: undefined,           // Remove reference dimensions
          _referenceHeight: undefined,          // Remove reference dimensions
        };

        return absolute;
      }
      return obj; // Return as-is if not image-relative
    })
  };

  return convertedAnnotations;
};

const loadImageBackground = async (
  canvas: Canvas,
  imageUrl: string
): Promise<void> => {
  return new Promise((resolve, reject) => {
    FabricImage.fromURL(imageUrl, {
      crossOrigin: "anonymous",
    })
      .then((img) => {
        // Set image at original size without scaling - resize function will handle scaling
        img.set({
          left: 0,
          top: 0,
          selectable: false,
          evented: false,
          name: "dicom-background",
        });

        canvas.backgroundImage = img;
        canvas.renderAll();
        resolve();
      })
      .catch(reject);
  });
};

const initializeCanvasElement = (canvasElement: HTMLCanvasElement) => {
  canvasElement.setAttribute("data-fabric-canvas", "true");
  canvasElement.removeAttribute("data-fabric");

  const container = canvasElement.parentElement;
  const containerWidth = container?.clientWidth || 512;
  const containerHeight = container?.clientHeight || 512;

  canvasElement.width = containerWidth;
  canvasElement.height = containerHeight;

  return { containerWidth, containerHeight };
};

export const createImageCanvas = async (
  canvasElement: HTMLCanvasElement,
  imageUrl: string,
  annotations?: unknown,
  defaultFilters?: FilterParams
): Promise<Canvas> => {
  const { containerWidth, containerHeight } =
    initializeCanvasElement(canvasElement);

  const canvas = new Canvas(canvasElement, {
    ...CANVAS_CONFIG,
    width: containerWidth,
    height: containerHeight,
  });

  if (defaultFilters) {
    initializeCanvasFilters(canvas, defaultFilters);
  }

  await loadImageBackground(canvas, imageUrl);

  if (defaultFilters) {
    applyFiltersToCanvas(canvas, defaultFilters);
  }

  if (annotations) {
    await loadAnnotations(canvas, annotations);
  }

  return canvas;
};



export const createAnnotationCanvas = async (
  canvasElement: HTMLCanvasElement,
  annotations?: unknown,
  defaultFilters?: FilterParams
): Promise<Canvas> => {
  const { containerWidth, containerHeight } =
    initializeCanvasElement(canvasElement);

  const canvas = new Canvas(canvasElement, {
    ...CANVAS_CONFIG,
    width: containerWidth,
    height: containerHeight,
  });

  if (defaultFilters) {
    initializeCanvasFilters(canvas, defaultFilters);
  }

  if (annotations) {
    await loadAnnotations(canvas, annotations);
  }

  return canvas;
};

const canvasFilterStates = new WeakMap<Canvas, FilterParams>();

export const initializeCanvasFilters = (
  canvas: Canvas,
  defaultFilters: FilterParams
): void => {
  canvasFilterStates.set(canvas, { ...defaultFilters });
};

export const getCanvasFilterState = (canvas: Canvas): FilterParams => {
  const state = canvasFilterStates.get(canvas);
  if (!state) {
    throw new Error(
      "Canvas filters not initialized. Call initializeCanvasFilters first."
    );
  }
  return state;
};

export const updateCanvasFilters = (
  canvas: Canvas,
  partialFilters: PartialFilterParams
): void => {
  const currentState = getCanvasFilterState(canvas);
  const newState = { ...currentState, ...partialFilters };
  canvasFilterStates.set(canvas, newState);
  applyFiltersToCanvas(canvas, newState);
};

const applyFiltersToCanvas = (canvas: Canvas, params: FilterParams): void => {
  const filterArray: any[] = [];

  if (params.brightness !== 1) {
    filterArray.push(
      new filters.Brightness({ brightness: params.brightness - 1 })
    );
  }

  if (params.contrast !== 1) {
    filterArray.push(new filters.Contrast({ contrast: params.contrast - 1 }));
  }

  if (params.grayscale) {
    filterArray.push(new filters.Grayscale());
  }

  if (params.invert) {
    filterArray.push(new filters.Invert());
  }

  if (params.sharpness !== 1) {
    const matrix =
      params.sharpness > 1
        ? [0, -1, 0, -1, 5, -1, 0, -1, 0]
        : [1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9];
    filterArray.push(new filters.Convolute({ matrix }));
  }

  if (params.gammaR !== 1 || params.gammaG !== 1 || params.gammaB !== 1) {
    filterArray.push(
      new filters.Gamma({
        gamma: [params.gammaR, params.gammaG, params.gammaB],
      })
    );
  }

  canvas.getObjects().forEach((obj) => {
    if (obj instanceof FabricImage) {
      obj.filters = [...filterArray];
      obj.applyFilters();
    }
  });

  if (canvas.backgroundImage instanceof FabricImage) {
    canvas.backgroundImage.filters = [...filterArray];
    canvas.backgroundImage.applyFilters();
  }

  canvas.renderAll();
};

/**
 * Clear all annotations from canvas while preserving the background image
 */
export const clearAnnotations = (canvas: Canvas): void => {
  if (!canvas) return;

  const backgroundImage = canvas.backgroundImage;

  // Remove all objects (annotations) but preserve the background image
  canvas.getObjects().forEach((obj) => {
    canvas.remove(obj);
  });

  // Restore the background image if it was set
  if (backgroundImage) {
    canvas.backgroundImage = backgroundImage;
  }

  canvas.renderAll();
};
