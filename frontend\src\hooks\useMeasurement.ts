import { useRef, useCallback } from 'react';
import { Canvas } from 'fabric';
import {
  createMeasurementState,
  startMeasurement,
  updateMeasurement,
  finalizeMeasurement,
  cancelMeasurement,
  toggleMeasurementUnit,
  type MeasurementState
} from '@/lib/fabric';

interface UseMeasurementProps {
  fabricCanvas: React.RefObject<Canvas | null>;
  onMeasurementCreated?: () => void;
}

export const useMeasurement = ({
  fabricCanvas,
  onMeasurementCreated
}: UseMeasurementProps) => {
  const measurementState = useRef<MeasurementState>(createMeasurementState());

  const handleMeasurementStart = useCallback((pointer: { x: number; y: number }) => {
    console.log('handleMeasurementStart called with pointer:', pointer);
    if (!fabricCanvas.current) {
      console.log('No fabric canvas available');
      return;
    }
    console.log('Calling startMeasurement');
    startMeasurement(fabricCanvas.current, pointer, measurementState.current);
  }, [fabricCanvas]);

  const handleMeasurementUpdate = useCallback((pointer: { x: number; y: number }) => {
    if (!fabricCanvas.current) return;
    updateMeasurement(fabricCanvas.current, pointer, measurementState.current);
  }, [fabricCanvas]);

  const handleMeasurementFinalize = useCallback(() => {
    if (!fabricCanvas.current) return;
    const result = finalizeMeasurement(fabricCanvas.current, measurementState.current);
    if (result && onMeasurementCreated) {
      onMeasurementCreated();
    }
  }, [fabricCanvas, onMeasurementCreated]);

  const handleMeasurementCancel = useCallback(() => {
    if (!fabricCanvas.current) return;
    cancelMeasurement(fabricCanvas.current, measurementState.current);
  }, [fabricCanvas]);

  const toggleUnit = useCallback((useMM: boolean) => {
    if (!fabricCanvas.current) return;
    measurementState.current.useMM = useMM;
    toggleMeasurementUnit(fabricCanvas.current, useMM);
  }, [fabricCanvas]);

  const clearAllMeasurements = useCallback(() => {
    if (!fabricCanvas.current) return;

    const canvas = fabricCanvas.current;
    const objects = canvas.getObjects();

    // Remove all measurement groups
    objects.forEach((obj) => {
      if ((obj as any).measurementGroup === true) {
        canvas.remove(obj);
      }
    });

    canvas.renderAll();
  }, [fabricCanvas]);

  const isDrawing = measurementState.current.isDrawing;
  const useMM = measurementState.current.useMM;

  return {
    isDrawing,
    useMM,
    handleMeasurementStart,
    handleMeasurementUpdate,
    handleMeasurementFinalize,
    handleMeasurementCancel,
    toggleUnit,
    clearAllMeasurements
  };
};
