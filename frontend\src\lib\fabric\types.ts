
export type UndoActionType =
  | "add"
  | "remove"
  | "modify"
  | "filter"
  | "rotate"
  | "flipH"
  | "flipV";

export interface BaseUndoAction {
  type: UndoActionType;
}

export interface AddUndoAction extends BaseUndoAction {
  type: "add";
  objectCount: number;
}

export interface RemoveUndoAction extends BaseUndoAction {
  type: "remove";
  objectCount: number;
}

export interface ModifyUndoAction extends BaseUndoAction {
  type: "modify";
  objectCount: number;
}

export interface FilterUndoAction extends BaseUndoAction {
  type: "filter";
  filters: {
    grayscale: boolean;
    invert: boolean;
  };
  annotations?: unknown;
}

export interface RotateUndoAction extends BaseUndoAction {
  type: "rotate";
}

export interface FlipHorizontalUndoAction extends BaseUndoAction {
  type: "flipH";
}

export interface FlipVerticalUndoAction extends BaseUndoAction {
  type: "flipV";
}

export type UndoAction =
  | AddUndoAction
  | RemoveUndoAction
  | ModifyUndoAction
  | FilterUndoAction
  | RotateUndoAction
  | FlipHorizontalUndoAction
  | FlipVerticalUndoAction;

export interface FilterState {
  grayscale: boolean;
  invert: boolean;
}

export type ToolMode =
  | "select"
  | "freehand"
  | "text"
  | "rect"
  | "line"
  | "circle"
  | "polygon";
