import { Canvas, Line, FabricText, Group } from "fabric";

const DPI = 96; // Assume 96 DPI (dots per inch)
const PIXELS_PER_MM = DPI / 25.4;

export interface MeasurementState {
  isDrawing: boolean;
  startPoint: { x: number; y: number } | null;
  tempLine: Line | null;
  tempText: FabricText | null;
  useMM: boolean;
}

export const createMeasurementState = (): MeasurementState => ({
  isDrawing: false,
  startPoint: null,
  tempLine: null,
  tempText: null,
  useMM: false,
});

export const startMeasurement = (
  canvas: Canvas,
  pointer: { x: number; y: number },
  state: MeasurementState
): void => {
  // Don't start if already drawing
  if (state.isDrawing) return;

  state.startPoint = { x: pointer.x, y: pointer.y };
  state.isDrawing = true;

  // Create temporary line starting and ending at the same point
  state.tempLine = new Line([pointer.x, pointer.y, pointer.x, pointer.y], {
    stroke: '#ff0000',
    strokeWidth: 2,
    selectable: false,
    evented: false,
    excludeFromExport: true,
  });

  // Create temporary text showing initial distance
  state.tempText = new FabricText('0 px', {
    left: pointer.x,
    top: pointer.y - 25,
    fontSize: 14,
    fill: '#ff0000',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    selectable: false,
    evented: false,
    excludeFromExport: true,
  });

  canvas.add(state.tempLine);
  canvas.add(state.tempText);
  canvas.renderAll();
};

export const updateMeasurement = (
  canvas: Canvas,
  pointer: { x: number; y: number },
  state: MeasurementState
): void => {
  if (!state.isDrawing || !state.startPoint || !state.tempLine || !state.tempText) return;

  const { x: startX, y: startY } = state.startPoint;

  // Update line endpoint
  state.tempLine.set({
    x2: pointer.x,
    y2: pointer.y
  });

  // Calculate distance
  const distancePx = Math.sqrt((pointer.x - startX) ** 2 + (pointer.y - startY) ** 2);
  const distance = state.useMM
    ? `${(distancePx / PIXELS_PER_MM).toFixed(2)} mm`
    : `${distancePx.toFixed(0)} px`;

  // Position text at midpoint
  const midX = (startX + pointer.x) / 2;
  const midY = (startY + pointer.y) / 2;

  state.tempText.set({
    text: distance,
    left: midX - 20, // Approximate center offset
    top: midY - 25,
  });

  canvas.renderAll();
};

export const finalizeMeasurement = (
  canvas: Canvas,
  state: MeasurementState
): Group | null => {
  if (!state.isDrawing || !state.tempLine || !state.tempText) return null;

  // Store the values before removing temporary objects
  const lineCoords: [number, number, number, number] = [
    state.tempLine.x1!,
    state.tempLine.y1!,
    state.tempLine.x2!,
    state.tempLine.y2!
  ];
  const textContent = state.tempText.text!;
  const textLeft = state.tempText.left!;
  const textTop = state.tempText.top!;

  // Remove temporary objects
  canvas.remove(state.tempLine);
  canvas.remove(state.tempText);

  // Create final line
  const finalLine = new Line(lineCoords, {
    stroke: '#ff0000',
    strokeWidth: 2,
    selectable: false, // Individual objects in group shouldn't be selectable
    evented: false,
  });

  // Create final text
  const finalText = new FabricText(textContent, {
    left: textLeft,
    top: textTop,
    fontSize: 14,
    fill: '#ff0000',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    selectable: false, // Individual objects in group shouldn't be selectable
    evented: false,
  });

  // Group line and text together
  const measurementGroup = new Group([finalLine, finalText], {
    selectable: true,
    evented: true,
  });

  // Add custom property to identify measurement groups
  (measurementGroup as any).measurementGroup = true;

  canvas.add(measurementGroup);

  // Reset state
  state.isDrawing = false;
  state.startPoint = null;
  state.tempLine = null;
  state.tempText = null;

  canvas.renderAll();
  return measurementGroup;
};

export const cancelMeasurement = (canvas: Canvas, state: MeasurementState): void => {
  if (state.tempLine) {
    canvas.remove(state.tempLine);
  }
  if (state.tempText) {
    canvas.remove(state.tempText);
  }

  state.isDrawing = false;
  state.startPoint = null;
  state.tempLine = null;
  state.tempText = null;

  canvas.renderAll();
};

export const toggleMeasurementUnit = (canvas: Canvas, useMM: boolean): void => {
  const objects = canvas.getObjects();

  objects.forEach((obj) => {
    if ((obj as any).measurementGroup === true && obj instanceof Group) {
      const groupObjects = obj.getObjects();
      const textObj = groupObjects.find(o => o instanceof FabricText) as FabricText;
      const lineObj = groupObjects.find(o => o instanceof Line) as Line;

      if (textObj && lineObj) {
        // Recalculate distance with new unit
        const distancePx = Math.sqrt(
          (lineObj.x2! - lineObj.x1!) ** 2 + (lineObj.y2! - lineObj.y1!) ** 2
        );

        const distance = useMM
          ? `${(distancePx / PIXELS_PER_MM).toFixed(2)} mm`
          : `${distancePx.toFixed(0)} px`;

        textObj.set({ text: distance });
      }
    }
  });

  canvas.renderAll();
};
