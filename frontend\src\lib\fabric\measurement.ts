import { Canvas, Line, FabricText, Group } from "fabric";

const DPI = 96; // Assume 96 DPI (dots per inch)
const PIXELS_PER_MM = DPI / 25.4;

export interface MeasurementState {
  isDrawing: boolean;
  startPoint: { x: number; y: number } | null;
  tempLine: any | null;
  tempText: any | null;
  useMM: boolean;
}

export const createMeasurementState = (): MeasurementState => ({
  isDrawing: false,
  startPoint: null,
  tempLine: null,
  tempText: null,
  useMM: false,
});

export const startMeasurement = (
  canvas: Canvas,
  pointer: { x: number; y: number },
  state: MeasurementState
): void => {
  state.startPoint = { x: pointer.x, y: pointer.y };
  state.isDrawing = true;

  // Create temporary line
  state.tempLine = new Line([pointer.x, pointer.y, pointer.x, pointer.y], {
    stroke: '#ff0000',
    strokeWidth: 2,
    selectable: false,
    evented: false,
    name: 'temp-measurement-line',
  } as any);

  // Create temporary text
  state.tempText = new FabricText('0', {
    left: pointer.x,
    top: pointer.y - 25,
    fontSize: 14,
    fill: '#ff0000',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    selectable: false,
    evented: false,
    name: 'temp-measurement-text',
  } as any);

  canvas.add(state.tempLine);
  canvas.add(state.tempText);
  canvas.renderAll();
};

export const updateMeasurement = (
  canvas: Canvas,
  pointer: { x: number; y: number },
  state: MeasurementState
): void => {
  if (!state.isDrawing || !state.startPoint || !state.tempLine || !state.tempText) return;

  const { x: startX, y: startY } = state.startPoint;

  // Update line endpoint
  state.tempLine.set({ x2: pointer.x, y2: pointer.y });

  // Calculate distance
  const distancePx = Math.sqrt((pointer.x - startX) ** 2 + (pointer.y - startY) ** 2);
  const distance = state.useMM
    ? `${(distancePx / PIXELS_PER_MM).toFixed(2)} mm`
    : `${distancePx.toFixed(0)} px`;

  // Position text at midpoint
  const midX = (startX + pointer.x) / 2;
  const midY = (startY + pointer.y) / 2;

  state.tempText.set({
    text: distance,
    left: midX - (state.tempText.width || 0) / 2,
    top: midY - 25,
  });

  canvas.renderAll();
};

export const finalizeMeasurement = (
  canvas: Canvas,
  state: MeasurementState
): Group | null => {
  if (!state.isDrawing || !state.tempLine || !state.tempText) return null;

  // Remove temporary objects
  canvas.remove(state.tempLine);
  canvas.remove(state.tempText);

  // Create final line with permanent styling
  const finalLine = new Line(
    [state.tempLine.x1!, state.tempLine.y1!, state.tempLine.x2!, state.tempLine.y2!],
    {
      stroke: '#ff0000',
      strokeWidth: 2,
      selectable: true,
      evented: true,
      name: 'measurement-line',
    } as any
  );

  // Create final text
  const finalText = new FabricText(state.tempText.text!, {
    left: state.tempText.left!,
    top: state.tempText.top!,
    fontSize: 14,
    fill: '#ff0000',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    selectable: true,
    evented: true,
    name: 'measurement-text',
  } as any);

  // Group line and text together
  const measurementGroup = new Group([finalLine, finalText], {
    selectable: true,
    evented: true,
    name: 'measurement-group',
  } as any);

  canvas.add(measurementGroup);

  // Reset state
  state.isDrawing = false;
  state.startPoint = null;
  state.tempLine = null;
  state.tempText = null;

  canvas.renderAll();
  return measurementGroup;
};

export const cancelMeasurement = (canvas: Canvas, state: MeasurementState): void => {
  if (state.tempLine) {
    canvas.remove(state.tempLine);
  }
  if (state.tempText) {
    canvas.remove(state.tempText);
  }

  state.isDrawing = false;
  state.startPoint = null;
  state.tempLine = null;
  state.tempText = null;

  canvas.renderAll();
};

export const toggleMeasurementUnit = (canvas: Canvas, useMM: boolean): void => {
  const objects = canvas.getObjects();
  
  objects.forEach((obj) => {
    if ((obj as any).name === 'measurement-group' && obj instanceof Group) {
      const textObj = obj.getObjects().find(o => (o as any).name === 'measurement-text') as any;
      const lineObj = obj.getObjects().find(o => (o as any).name === 'measurement-line') as Line;
      
      if (textObj && lineObj) {
        // Recalculate distance with new unit
        const distancePx = Math.sqrt(
          (lineObj.x2! - lineObj.x1!) ** 2 + (lineObj.y2! - lineObj.y1!) ** 2
        );
        
        const distance = useMM
          ? `${(distancePx / PIXELS_PER_MM).toFixed(2)} mm`
          : `${distancePx.toFixed(0)} px`;
        
        textObj.set({ text: distance });
      }
    }
  });
  
  canvas.renderAll();
};
