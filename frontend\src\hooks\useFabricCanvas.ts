import { useRef, useState, useCallback } from "react";
import { Canvas } from "fabric";
import {
  createImageCanvas,
  rotateCanvas,
  flipCanvasHorizontal,
  flipCanvasVertical,
  clearAnnotations,
  type UndoAction,
  type FilterUndoAction,
  type AddUndoAction,
  type RotateUndoAction,
  type FlipHorizontalUndoAction,
  type FlipVerticalUndoAction,
} from "@/lib/fabric";

interface UseFabricCanvasProps {
  onShapeCreated?: () => void;
}

export const useFabricCanvas = ({
  onShapeCreated,
}: UseFabricCanvasProps = {}) => {
  const fabricCanvas = useRef<Canvas | null>(null);
  const eventDisposers = useRef<(() => void)[]>([]);
  const isUndoing = useRef(false);
  const initialObjectCount = useRef(0);
  const isDestroyed = useRef(false);
  const [undoStack, setUndoStack] = useState<UndoAction[]>([]);

  const setupCanvas = useCallback(
    async (
      canvasElement: HTMLCanvasElement,
      imageSource: string,
      annotations: any,
      defaultFilters: any
    ) => {
      // Reset destruction flag when setting up new canvas
      isDestroyed.current = false;

      fabricCanvas.current = await createImageCanvas(
        canvasElement,
        imageSource,
        annotations,
        defaultFilters
      );

      const canvas = fabricCanvas.current;
      initialObjectCount.current = canvas.getObjects().length;

      const trackAnnotationAdd = () => {
        if (!isUndoing.current && canvas) {
          const allObjects = canvas.getObjects();
          const newObjectsCount =
            allObjects.length - initialObjectCount.current;
          if (newObjectsCount > 0) {
            setUndoStack((prev) => [
              ...prev.slice(-9),
              { type: "add", objectCount: allObjects.length },
            ]);
          }
        }
      };

      const trackAnnotationRemove = () => {
        if (!isUndoing.current && canvas) {
          const allObjects = canvas.getObjects();
          setUndoStack((prev) => [
            ...prev.slice(-9),
            { type: "remove", objectCount: allObjects.length },
          ]);
        }
      };

      eventDisposers.current.forEach((dispose) => dispose());
      eventDisposers.current = [];

      eventDisposers.current = [
        canvas.on("path:created", trackAnnotationAdd),
        canvas.on("object:removed", trackAnnotationRemove),
        canvas.on("object:modified", trackAnnotationAdd),
      ];

      return canvas;
    },
    [setUndoStack]
  );

  const addToUndoStack = useCallback((action: UndoAction) => {
    setUndoStack((prev) => [...prev.slice(-9), action]);
  }, []);

  const handleUndo = useCallback(
    (updateFilter: (key: string, value: any) => void) => {
      if (
        !fabricCanvas.current ||
        undoStack.length === 0 ||
        isDestroyed.current
      )
        return;

      const canvas = fabricCanvas.current;
      const lastAction = undoStack[undoStack.length - 1];

      isUndoing.current = true;

      switch (lastAction.type) {
        case "add":
          const objects = canvas.getObjects();
          if (objects.length > initialObjectCount.current) {
            const lastObject = objects[objects.length - 1];
            canvas.remove(lastObject);
            canvas.renderAll();
          }
          break;

        case "filter":
          const filters = (lastAction as FilterUndoAction).filters;
          updateFilter("grayscale", filters.grayscale);
          updateFilter("invert", filters.invert);
          break;

        case "rotate":
          rotateCanvas(canvas);
          rotateCanvas(canvas);
          rotateCanvas(canvas);
          break;

        case "flipH":
          flipCanvasHorizontal(canvas);
          break;

        case "flipV":
          flipCanvasVertical(canvas);
          break;

        default:
          break;
      }

      setUndoStack((prev) => prev.slice(0, -1));
      isUndoing.current = false;
    },
    [undoStack]
  );

  const handleRotate = useCallback(() => {
    if (!fabricCanvas.current || isDestroyed.current) return;
    const rotateAction: RotateUndoAction = { type: "rotate" };
    addToUndoStack(rotateAction);
    rotateCanvas(fabricCanvas.current);
  }, [addToUndoStack]);

  const handleFlipHorizontal = useCallback(() => {
    if (!fabricCanvas.current || isDestroyed.current) return;
    const flipAction: FlipHorizontalUndoAction = { type: "flipH" };
    addToUndoStack(flipAction);
    flipCanvasHorizontal(fabricCanvas.current);
  }, [addToUndoStack]);

  const handleFlipVertical = useCallback(() => {
    if (!fabricCanvas.current || isDestroyed.current) return;
    const flipAction: FlipVerticalUndoAction = { type: "flipV" };
    addToUndoStack(flipAction);
    flipCanvasVertical(fabricCanvas.current);
  }, [addToUndoStack]);

  const handleShapeCreated = useCallback(() => {
    if (!fabricCanvas.current || isDestroyed.current) return;
    const addAction: AddUndoAction = {
      type: "add",
      objectCount: fabricCanvas.current.getObjects().length,
    };
    addToUndoStack(addAction);
    onShapeCreated?.();
  }, [addToUndoStack, onShapeCreated]);

  return {
    fabricCanvas,
    undoStack,
    setupCanvas,
    addToUndoStack,
    handleUndo,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    handleShapeCreated,
    canUndo: undoStack.length > 0,
  };
};
