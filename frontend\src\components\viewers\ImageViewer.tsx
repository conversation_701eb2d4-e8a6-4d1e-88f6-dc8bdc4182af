import { useEffect, useRef, useState } from "react";
import FabricToolbar from "@/components/toolbars/FabricToolbar";
import { MedicalImageViewerProps } from "@/shared/types";
import { useFabricViewer } from "@/hooks/useFabricViewer";
import { useFabricResize } from "@/hooks/useFabricResize";

const ImageViewer: React.FC<MedicalImageViewerProps> = ({ data }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const {
    fabricCanvas,
    setupFabricCanvas,
    brightness,
    contrast,
    grayscale,
    invert,
    sharpness,
    gammaR,
    gammaG,
    gammaB,
    handleBrightnessChange,
    handleContrastChange,
    handleGrayscaleChange,
    handleInvertChange,
    handleSharpnessChange,
    handleGammaRChange,
    handleGammaGChange,
    handleGammaBChange,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    handleUndo,
    handleShapeCreated,
    handleSave,
    canUndo,
  } = useFabricViewer({
    viewerType: "image",
    data,
  });

  // Handle viewport resize for responsive behavior
  const { triggerResize } = useFabricResize({
    fabricCanvasRef: fabricCanvas,
    containerSelector: "#image-viewer-container .viewer-container",
    isInitialized,
  });

  useEffect(() => {
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (!isInitialized || !canvasRef.current) return;

    async function setupViewer() {
      await setupFabricCanvas(canvasRef.current!);
      triggerResize();
    }

    setupViewer();
  }, [isInitialized, data.viewer.imageUrl]);

  return (
    <div className="image-viewer" id="image-viewer-container">
      <div className="viewer-container">
        <canvas ref={canvasRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gammaR={gammaR}
        gammaG={gammaG}
        gammaB={gammaB}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaRChange={handleGammaRChange}
        onGammaGChange={handleGammaGChange}
        onGammaBChange={handleGammaBChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onUndo={handleUndo}
        canUndo={canUndo}
        onSave={handleSave}
        onShapeCreated={handleShapeCreated}
      />
    </div>
  );
};

export default ImageViewer;
