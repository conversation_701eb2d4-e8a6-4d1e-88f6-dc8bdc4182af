* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  color: #2c3e50;
}

.home-container {
  padding: 50px 20px;
  text-align: center;
}

.home-title {
  margin-bottom: 20px;
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.8px;
  color: #4a5568;
  line-height: 1.2;
}

.home-description {
  margin-bottom: 30px;
  color: #4a5568;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.5;
}

.home-button {
  padding: 14px 28px;
  background: #007acc;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: none;

  &:hover {
    background: #005a9e;
  }
}

.home-button-link {
  text-decoration: none;
  color: inherit;
}

.viewer-base {
  width: 100%;
  height: 100%;
  background: #000;
  color: white;
  position: relative;
  overflow: hidden;
}

.viewer-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.cornerstone-element {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #000;
  object-fit: contain;
}

.controls {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  padding: 15px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  width: 250px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 10;

  .control-group {
    margin-bottom: 15px;

    label {
      display: block;
      margin-bottom: 8px;
      font-size: 13px;
      font-weight: 600;
      letter-spacing: 0.25px;
      color: #fff;
      text-transform: uppercase;
    }

    input[type="range"] {
      width: 100%;
      margin-bottom: 5px;
      accent-color: #007acc;
    }

    span {
      font-size: 12px;
      color: #fff;
      opacity: 0.8;
    }
  }

  .save-button {
    width: 100%;
    padding: 8px;
    background: #007acc;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;

    &:hover {
      background: #005a9e;
    }
  }
}

.stack-viewer {
  @extend .viewer-base;
}

.volume-viewer {
  @extend .viewer-base;

  .mode-switcher {
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 12px;
    color: white;
    cursor: pointer;
    z-index: 10;

    &-item {
      &.active {
        color: #4CAF50;
      }

      &.inactive {
        color: #888;
      }
    }

    &-separator {
      margin: 0 5px;
    }
  }
}

.image-viewer {
  @extend .viewer-base;
}

.patient-viewer {
  display: flex;
  height: 100vh;
  overflow: hidden;

  .file-list {
    width: 250px;
    min-width: 250px;
    max-width: 250px;
    flex-shrink: 0;
    border-right: 1px solid #ccc;
    overflow-y: auto;
    height: 100%;
    background: #f5f5f5;

    .patient-header {
      padding: 15px;
      border-bottom: 1px solid #ccc;
      background: #d0d0d0;

      h3 {
        margin: 0 0 8px 0;
        color: #1a202c;
        font-size: 20px;
        font-weight: 700;
        letter-spacing: -0.4px;
        line-height: 1.3;
      }

      .patient-details {
        font-size: 14px;
        color: #4a5568;
        font-weight: 500;
        line-height: 1.4;
        letter-spacing: 0.1px;
      }
    }

    .file-list-header {
      padding: 10px 15px;
      border-bottom: 1px solid #ccc;
      background: #e5e5e5;

      h4 {
        margin: 0;
        color: #2d3748;
        font-size: 15px;
        font-weight: 700;
        letter-spacing: 0.2px;
        text-transform: uppercase;
      }

      .file-count {
        font-size: 13px;
        color: #4a5568;
        font-weight: 500;
        letter-spacing: 0.1px;
      }
    }

    .file-items {
      .file-item {
        padding: 10px 15px;
        cursor: pointer;
        border-bottom: 1px solid #ccc;
        transition: background-color 0.2s ease;

        &:hover {
          background: #e5e5e5;
        }

        &.selected {
          background: #007acc;
          color: white;

          .file-name {
            color: white;
          }
        }

        .file-name {
          font-weight: 600;
          margin-bottom: 4px;
          font-size: 15px;
          letter-spacing: -0.2px;
          color: #2d3748;
          line-height: 1.3;
        }

        .file-type {
          font-size: 11px;
          color: #718096;
          text-transform: uppercase;
          font-weight: 600;
          letter-spacing: 0.8px;
        }
      }
    }
  }

  .viewer-panel {
    flex: 1;
    min-width: 0;
    background: white;
    position: relative;
    height: 100%;
    overflow: hidden;

    .empty-state,
    .loading-state,
    .error-state {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #666;
    }
  }
}

.fabric-toolbar-vertical {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 160px;
  max-width: 160px;
  background: rgba(0, 0, 0, 0.95);
  border-radius: 8px;
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  z-index: 1000;
  padding: 8px;
  box-sizing: border-box;
  overflow: hidden;



  .control-item {
    margin-bottom: 6px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
    width: 100%;
    box-sizing: border-box;

    .control-icon {
      color: #fff;
      font-size: 12px;
      width: 16px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .horizontal-slider {
      flex: 1;
      min-width: 0;
      height: 4px;
      background: rgba(255, 255, 255, 0.1);
      outline: none;
      border-radius: 2px;
      accent-color: #007acc;

      &::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007acc;
        cursor: pointer;
        border: 1px solid #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }

      &::-moz-range-thumb {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007acc;
        cursor: pointer;
        border: 1px solid #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }
    }

    span {
      font-size: 7px;
      color: #fff;
      font-weight: 600;
      background: rgba(255, 255, 255, 0.1);
      padding: 1px 2px;
      border-radius: 2px;
      width: 20px;
      flex-shrink: 0;
      text-align: center;
      overflow: hidden;
    }
  }

  .gamma-controls {
    margin-bottom: 8px;

    .gamma-icon {
      color: #fff;
      font-size: 12px;
      text-align: center;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .gamma-sliders {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .gamma-slider-item {
      display: flex;
      align-items: center;
      gap: 4px;
      width: 100%;
      box-sizing: border-box;

      .gamma-label {
        font-size: 8px;
        font-weight: 700;
        width: 12px;
        flex-shrink: 0;
        text-align: center;
        padding: 1px 2px;
        border-radius: 2px;

        &.red {
          background: rgba(255, 99, 99, 0.2);
          color: #ff6363;
          border: 1px solid rgba(255, 99, 99, 0.3);
        }

        &.green {
          background: rgba(99, 255, 99, 0.2);
          color: #63ff63;
          border: 1px solid rgba(99, 255, 99, 0.3);
        }

        &.blue {
          background: rgba(99, 99, 255, 0.2);
          color: #6363ff;
          border: 1px solid rgba(99, 99, 255, 0.3);
        }
      }

      .horizontal-slider {
        flex: 1;
        min-width: 0;

        &.gamma-red {
          accent-color: #ff6363;
        }

        &.gamma-green {
          accent-color: #63ff63;
        }

        &.gamma-blue {
          accent-color: #6363ff;
        }
      }

      .gamma-value {
        font-size: 6px;
        width: 18px;
        flex-shrink: 0;
        text-align: center;
        overflow: hidden;
      }
    }

    &.disabled {
      opacity: 0.4;
      pointer-events: none;

      .gamma-sliders {
        filter: grayscale(1);
      }

      input[type="range"] {
        cursor: not-allowed;
      }

      .gamma-value {
        opacity: 0.6;
      }
    }
  }

  .save-btn {
    background: #007acc;
    color: white;
    border: none;
    padding: 4px 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    width: 100%;
    border-radius: 3px;
    margin-top: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #005a9e;
      transform: translateY(-1px);
    }

    &.export-btn {
      background: #8b5cf6;
      margin-top: 3px;

      &:hover {
        background: #7c3aed;
      }
    }

    &.undo-btn {
      background: #f59e0b;
      margin-top: 3px;

      &:hover:not(.disabled) {
        background: #d97706;
      }

      &.disabled {
        background: rgba(245, 158, 11, 0.3);
        cursor: not-allowed;
        opacity: 0.5;

        &:hover {
          background: rgba(245, 158, 11, 0.3);
          transform: none;
        }
      }
    }
  }

  .annotation-tools {
    margin-bottom: 6px;

    .tool-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 3px;
      margin-bottom: 4px;
    }

    .tool-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: #fff;
      padding: 4px 2px;
      cursor: pointer;
      border-radius: 2px;
      font-size: 10px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 20px;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-1px);
      }

      &.active {
        background: #007acc;
        border-color: #007acc;
        color: white;
      }

      &.disabled {
        opacity: 0.4;
        cursor: not-allowed;
        background: rgba(255, 255, 255, 0.05);

        &:hover {
          background: rgba(255, 255, 255, 0.05);
          transform: none;
        }
      }
    }

    .clear-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 3px 4px;
      cursor: pointer;
      font-size: 10px;
      transition: all 0.2s ease;
      width: 100%;
      border-radius: 2px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #c82333;
        transform: translateY(-1px);
      }
    }

    .utility-tools {
      margin-top: 4px;
      padding-top: 4px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .measurement-controls {
      margin-top: 4px;
      padding-top: 4px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);

      .measurement-unit-toggle {
        margin-bottom: 4px;

        label {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 10px;
          color: #fff;
          cursor: pointer;

          input[type="checkbox"] {
            width: 12px;
            height: 12px;
            accent-color: #007acc;
          }
        }
      }

      .clear-measurements-btn {
        background: #f59e0b;
        color: white;
        border: none;
        padding: 3px 4px;
        cursor: pointer;
        font-size: 9px;
        transition: all 0.2s ease;
        width: 100%;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: #d97706;
          transform: translateY(-1px);
        }
      }
    }
  }

  .measurement-controls {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    .measurement-unit-toggle {
      margin-bottom: 6px;

      label {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 10px;
        color: #fff;
        cursor: pointer;

        input[type="checkbox"] {
          width: 12px;
          height: 12px;
          accent-color: #007acc;
        }
      }
    }

    .clear-measurements-btn {
      background: rgba(255, 99, 99, 0.8);
      color: white;
      border: none;
      padding: 3px 6px;
      cursor: pointer;
      font-size: 9px;
      border-radius: 3px;
      width: 100%;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 99, 99, 1);
      }
    }
  }
}


