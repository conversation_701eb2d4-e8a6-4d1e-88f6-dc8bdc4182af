/**
 * Utility functions for Fabric.js canvas operations
 */

/**
 * Extracts default filter values from viewer configuration
 */
export const extractDefaultFilters = (fabricConfigs: any) => {
  return {
    brightness: fabricConfigs.brightness,
    contrast: fabricConfigs.contrast,
    grayscale: fabricConfigs.grayscale,
    invert: fabricConfigs.invert,
    sharpness: fabricConfigs.sharpness,
    gammaR: fabricConfigs.gammaR,
    gammaG: fabricConfigs.gammaG,
    gammaB: fabricConfigs.gammaB,
  };
};



/**
 * Creates save configuration object for API calls with image-relative positioning
 */
export const createSaveConfig = (filters: any, fabricCanvas: any) => {
  let fabricCanvasData = {};

  if (fabricCanvas) {
    // Convert annotations to image-relative coordinates before saving
    fabricCanvasData = convertCanvasToImageRelative(fabricCanvas);
  }

  return {
    ...filters,
    annotations: fabricCanvasData,
  };
};

/**
 * Convert canvas annotations to image-relative coordinates
 */
const convertCanvasToImageRelative = (canvas: any) => {
  const canvasData = canvas.toJSON();
  const backgroundImage = canvas.backgroundImage;

  if (!backgroundImage) {
    return canvasData;
  }

  const imageWidth = backgroundImage.width;
  const imageHeight = backgroundImage.height;

  // Convert each annotation to image-relative coordinates
  canvasData.objects = canvasData.objects.map((obj: any) => {
    return {
      ...obj,
      left: obj.left / imageWidth,      // Convert to 0-1 range relative to image
      top: obj.top / imageHeight,       // Convert to 0-1 range relative to image
      scaleX: obj.scaleX || 1,          // Keep scale as-is
      scaleY: obj.scaleY || 1,          // Keep scale as-is
      _imageRelative: true,             // Mark as image-relative
      _referenceWidth: imageWidth,      // Store reference dimensions
      _referenceHeight: imageHeight,    // Store reference dimensions
    };
  });

  return canvasData;
};
