import { useCallback } from "react";
import { Canvas } from "fabric";
import { updateCanvasFilters, type FilterUndoAction } from "@/lib/fabric";
import { type FilterState } from "./useViewerFilters";

interface UseFabricFiltersProps {
  fabricCanvas: React.RefObject<Canvas | null>;
  updateFilter: (key: keyof FilterState, value: number | boolean) => void;
  addToUndoStack: (action: FilterUndoAction) => void;
  filters: FilterState;
}

export const useFabricFilters = ({
  fabricCanvas,
  updateFilter,
  addToUndoStack,
  filters,
}: UseFabricFiltersProps) => {
  const { grayscale, invert } = filters;

  const saveFilterState = useCallback(() => {
    if (!fabricCanvas.current) return;
    const currentState: FilterUndoAction = {
      type: "filter" as const,
      filters: { grayscale, invert },
      annotations: fabricCanvas.current.toJSON(),
    };
    addToUndoStack(currentState);
  }, [fabricCanvas, grayscale, invert, addToUndoStack]);

  const handleBrightnessChange = useCallback(
    (value: number) => {
      updateFilter("brightness", value);
      if (fabricCanvas.current) {
        updateCanvasFilters(fabricCanvas.current, { brightness: value });
      }
    },
    [updateFilter, fabricCanvas]
  );

  const handleContrastChange = useCallback(
    (value: number) => {
      updateFilter("contrast", value);
      if (fabricCanvas.current) {
        updateCanvasFilters(fabricCanvas.current, { contrast: value });
      }
    },
    [updateFilter, fabricCanvas]
  );

  const handleGrayscaleChange = useCallback(
    (value: boolean) => {
      saveFilterState();
      updateFilter("grayscale", value);
      if (fabricCanvas.current) {
        updateCanvasFilters(fabricCanvas.current, { grayscale: value });
      }
    },
    [saveFilterState, updateFilter, fabricCanvas]
  );

  const handleInvertChange = useCallback(
    (value: boolean) => {
      saveFilterState();
      updateFilter("invert", value);
      if (fabricCanvas.current) {
        updateCanvasFilters(fabricCanvas.current, { invert: value });
      }
    },
    [saveFilterState, updateFilter, fabricCanvas]
  );

  const handleSharpnessChange = useCallback(
    (value: number) => {
      updateFilter("sharpness", value);
      if (fabricCanvas.current) {
        updateCanvasFilters(fabricCanvas.current, { sharpness: value });
      }
    },
    [updateFilter, fabricCanvas]
  );

  const handleGammaRChange = useCallback(
    (value: number) => {
      updateFilter("gammaR", value);
      if (fabricCanvas.current) {
        updateCanvasFilters(fabricCanvas.current, { gammaR: value });
      }
    },
    [updateFilter, fabricCanvas]
  );

  const handleGammaGChange = useCallback(
    (value: number) => {
      updateFilter("gammaG", value);
      if (fabricCanvas.current) {
        updateCanvasFilters(fabricCanvas.current, { gammaG: value });
      }
    },
    [updateFilter, fabricCanvas]
  );

  const handleGammaBChange = useCallback(
    (value: number) => {
      updateFilter("gammaB", value);
      if (fabricCanvas.current) {
        updateCanvasFilters(fabricCanvas.current, { gammaB: value });
      }
    },
    [updateFilter, fabricCanvas]
  );

  return {
    handleBrightnessChange,
    handleContrastChange,
    handleGrayscaleChange,
    handleInvertChange,
    handleSharpnessChange,
    handleGammaRChange,
    handleGammaGChange,
    handleGammaBChange,
  };
};
