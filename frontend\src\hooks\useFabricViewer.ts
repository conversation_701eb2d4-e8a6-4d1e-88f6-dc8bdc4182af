import { useCallback } from "react";
import { useFabricCanvas } from "./useFabricCanvas";
import { useFabricFilters } from "./useFabricFilters";
import { useViewerFilters, type FilterState } from "./useViewerFilters";
import { saveFabricConfig } from "@/shared/api";
import { extractDefaultFilters, createSaveConfig } from "@/utils/fabricUtils";

interface UseFabricViewerProps {
  viewerType: "image" | "stack";
  data: {
    id: string;
    viewer: {
      fabricConfigs: any;
      imageUrl: string;
    };
  };
  onShapeCreated?: () => void;
}

export const useFabricViewer = ({
  viewerType,
  data,
  onShapeCreated,
}: UseFabricViewerProps) => {
  const defaultFilters = extractDefaultFilters(data.viewer.fabricConfigs);
  const defaultAnnotations = data.viewer.fabricConfigs.annotations;

  const { filters, updateFilter } =
    useViewerFilters(viewerType, {
      initialFilters: defaultFilters,
      initialAnnotations: defaultAnnotations,
    });

  const {
    fabricCanvas,
    undoStack,
    setupCanvas,
    addToUndoStack,
    handleUndo: baseHandleUndo,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    handleShapeCreated,
    canUndo,
  } = useFabricCanvas({ onShapeCreated });

  const filterHandlers = useFabricFilters({
    fabricCanvas,
    updateFilter,
    addToUndoStack,
    filters,
  });

  const handleUndo = useCallback(() => {
    const updateFilterForUndo = (key: string, value: any) => {
      updateFilter(key as keyof FilterState, value);
    };
    baseHandleUndo(updateFilterForUndo);
  }, [baseHandleUndo, updateFilter]);

  const handleSave = useCallback(async () => {
    const saveConfig = createSaveConfig(filters, fabricCanvas.current);
    await saveFabricConfig(data.id, saveConfig);
  }, [filters, fabricCanvas, data.id]);

  const setupFabricCanvas = useCallback(
    async (canvasElement: HTMLCanvasElement, imageSource?: string) => {
      const source = imageSource || data.viewer.imageUrl;

      return await setupCanvas(
        canvasElement,
        source,
        defaultAnnotations,
        defaultFilters
      );
    },
    [setupCanvas, data.viewer, defaultAnnotations, defaultFilters]
  );

  return {
    // Canvas management
    fabricCanvas,
    setupFabricCanvas,

    // Filter state
    filters,
    updateFilter,

    // Filter handlers
    ...filterHandlers,

    // Canvas operations
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    handleShapeCreated,

    // Undo system
    handleUndo,
    canUndo,
    undoStack,

    // Save functionality
    handleSave,

    // Destructured filter values for convenience
    brightness: filters.brightness,
    contrast: filters.contrast,
    grayscale: filters.grayscale,
    invert: filters.invert,
    sharpness: filters.sharpness,
    gammaR: filters.gammaR,
    gammaG: filters.gammaG,
    gammaB: filters.gammaB,
  };
};
