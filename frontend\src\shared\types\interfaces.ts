import { Canvas } from "fabric";

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
}

export interface DicomStackData {
  id: string;
  viewer: {
    imageUrl: string;
    fabricConfigs: FabricConfig;
  };
}

export interface DicomVolumeData {
  id: string;
  viewer: {
    imageUrl: string[];
    configs: DicomVolumeConfig;
  };
}

export interface MedicalImageData {
  id: string;
  viewer: {
    imageUrl: string;
    fabricConfigs: FabricConfig;
  };
}

export interface DicomStackViewerProps {
  data: DicomStackData;
}

export interface DicomVolumeViewerProps {
  data: DicomVolumeData;
}

export interface MedicalImageViewerProps {
  data: MedicalImageData;
}

export interface FabricConfig {
  contrast: number;
  brightness: number;
  grayscale: boolean;
  invert: boolean;
  sharpness: number;
  gammaR: number;
  gammaG: number;
  gammaB: number;
  annotations: any;
}

export interface DicomVolumeConfig {
  shift: number;
}
export interface MedicalFileItem {
  id: string;
  name: string;
  type: "stack" | "volume" | "image";
}

export interface PatientDetails {
  patientId: string;
  patientName: string;
  files: MedicalFileItem[];
}

export interface FabricToolbarProps {
  fabricCanvas: React.MutableRefObject<Canvas | null>;
  brightness: number;
  contrast: number;
  grayscale: boolean;
  invert: boolean;
  sharpness: number;
  gammaR: number;
  gammaG: number;
  gammaB: number;
  onBrightnessChange: (value: number) => void;
  onContrastChange: (value: number) => void;
  onGrayscaleChange: (value: boolean) => void;
  onInvertChange: (value: boolean) => void;
  onSharpnessChange: (value: number) => void;
  onGammaRChange: (value: number) => void;
  onGammaGChange: (value: number) => void;
  onGammaBChange: (value: number) => void;
  onRotate?: () => void;
  onFlipHorizontal?: () => void;
  onFlipVertical?: () => void;
  onUndo?: () => void;
  canUndo?: boolean;
  onSave?: () => void;
  onShapeCreated?: () => void;
  disableGrayscale?: boolean;
  disableGamma?: boolean;
  disableUndoTracking?: () => void;
  enableUndoTracking?: () => void;
}