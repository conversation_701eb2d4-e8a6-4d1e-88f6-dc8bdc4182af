import { useEffect, useRef, useState } from "react";
import { Types, Enums } from "@cornerstonejs/core";
import FabricToolbar from "@/components/toolbars/FabricToolbar";
import {
  initializeCornerstone,
  createRenderingEngine,
  setup2dViewport,
  loadDicomStack,
} from "@/lib/dicom";
import { DicomStackViewerProps } from "@/shared/types";
import { useFabricViewer } from "@/hooks/useFabricViewer";
import { useFabricResize } from "@/hooks/useFabricResize";

const StackViewer: React.FC<DicomStackViewerProps> = ({ data }) => {
  const cornerstoneRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const viewportRef = useRef<Types.IStackViewport | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasFabricCanvas, setHasFabricCanvas] = useState(false);

  const {
    fabricCanvas,
    setupFabricCanvas,
    brightness,
    contrast,
    grayscale,
    invert,
    sharpness,
    gammaR,
    gammaG,
    gammaB,
    handleBrightnessChange,
    handleContrastChange,
    handleGrayscaleChange,
    handleInvertChange,
    handleSharpnessChange,
    handleGammaRChange,
    handleGammaGChange,
    handleGammaBChange,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    handleUndo,
    handleShapeCreated,
    handleSave,
    canUndo,
  } = useFabricViewer({
    viewerType: "stack",
    data,
  });

  // Handle viewport resize for responsive behavior
  const { triggerResize } = useFabricResize({
    fabricCanvasRef: fabricCanvas,
    containerSelector: "#stack-viewer-container .viewer-container",
    isInitialized: hasFabricCanvas,
  });

  const captureAndTransferToFabric = async () => {
    if (!cornerstoneRef.current || !canvasRef.current) return;

    const cornerstoneCanvas = cornerstoneRef.current.querySelector("canvas");
    if (!cornerstoneCanvas) {
      return;
    }

    const imageDataUrl = cornerstoneCanvas.toDataURL("image/png");

    await setupFabricCanvas(canvasRef.current, imageDataUrl);

    // Mark that Fabric canvas is now active
    setHasFabricCanvas(true);
  };

  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);

    return () => {
      renderingEngineRef.current?.destroy();
    };
  }, []);

  // Trigger resize when Fabric canvas becomes available
  useEffect(() => {
    if (hasFabricCanvas) {
      triggerResize();
    }
  }, [hasFabricCanvas, triggerResize]);

  useEffect(() => {
    if (!isInitialized || !cornerstoneRef.current) return;

    const element = cornerstoneRef.current;

    async function setupViewer() {
      const renderingEngineId = `stackViewer_${Date.now()}`;
      const currentViewportId = `viewport_${Date.now()}`;

      const renderingEngine = createRenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;

      const viewport = setup2dViewport(
        renderingEngine,
        element,
        currentViewportId
      );
      viewportRef.current = viewport;
      const handleImageRendered = () => {
        captureAndTransferToFabric();
        element.removeEventListener(
          Enums.Events.IMAGE_RENDERED,
          handleImageRendered
        );
      };

      element.addEventListener(
        Enums.Events.IMAGE_RENDERED,
        handleImageRendered
      );

      await loadDicomStack(viewport, data.viewer.imageUrl);
    }
    setupViewer();
  }, [isInitialized, data.viewer.imageUrl]);

  return (
    <div className="stack-viewer" id="stack-viewer-container">
      <div className="viewer-container">
        {!hasFabricCanvas && (
          <div ref={cornerstoneRef} className="cornerstone-element" />
        )}
        <canvas
          ref={canvasRef}
          className="fabric-canvas"
          style={{ opacity: hasFabricCanvas ? 1 : 0 }}
        />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gammaR={gammaR}
        gammaG={gammaG}
        gammaB={gammaB}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaRChange={handleGammaRChange}
        onGammaGChange={handleGammaGChange}
        onGammaBChange={handleGammaBChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onUndo={handleUndo}
        canUndo={canUndo}
        onSave={handleSave}
        onShapeCreated={handleShapeCreated}
        disableGrayscale={true}
        disableGamma={true}
      />
    </div>
  );
};

export default StackViewer;
