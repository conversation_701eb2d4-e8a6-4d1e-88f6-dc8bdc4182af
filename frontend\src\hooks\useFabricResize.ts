import { useEffect, useRef, useCallback } from "react";
import { Canvas } from "fabric";

interface UseFabricResizeProps {
  fabricCanvasRef: React.RefObject<Canvas | null>;
  containerSelector: string;
  isInitialized: boolean;
}

export function useFabricResize({
  fabricCanvasRef,
  containerSelector,
  isInitialized,
}: UseFabricResizeProps): { triggerResize: () => void } {
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const rafIdRef = useRef<number | null>(null);
  const lastSizeRef = useRef<{ width: number; height: number } | null>(null);

  // Optimized resize handler with RAF and size change detection
  const handleResize = useCallback(
    (entries?: ResizeObserverEntry[]) => {
      // Cancel any pending animation frame
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }

      rafIdRef.current = requestAnimationFrame(() => {
        if (!fabricCanvasRef.current || !isInitialized) return;

        const canvas = fabricCanvasRef.current;
        let container: Element | null;
        let containerWidth: number;
        let containerHeight: number;

        if (entries && entries.length > 0) {
          // Use ResizeObserver data for better performance
          const entry = entries[0];
          container = entry.target;
          const { width, height } = entry.contentRect;
          containerWidth = width;
          containerHeight = height;
        } else {
          // Fallback to DOM query (for window resize)
          container = document.querySelector(containerSelector);
          if (!container) return;
          const containerElement = container as HTMLElement;
          containerWidth = containerElement.clientWidth;
          containerHeight = containerElement.clientHeight;
        }

        // Skip if size hasn't changed (prevents unnecessary renders)
        if (
          lastSizeRef.current &&
          lastSizeRef.current.width === containerWidth &&
          lastSizeRef.current.height === containerHeight
        ) {
          return;
        }

        lastSizeRef.current = {
          width: containerWidth,
          height: containerHeight,
        };

        // Get image dimensions for proper scaling
        const backgroundImage = canvas.backgroundImage;
        if (!backgroundImage) return;
        const imageWidth = backgroundImage.width!;
        const imageHeight = backgroundImage.height!;
        const aspectRatio = imageWidth / imageHeight;

        // Calculate scale to fit container while maintaining aspect ratio
        let newWidth = containerWidth;
        let newHeight = containerHeight;

        if (containerWidth / containerHeight > aspectRatio) {
          newWidth = containerHeight * aspectRatio;
        } else {
          newHeight = containerWidth / aspectRatio;
        }

        // Calculate scale factor and centering offset
        const scale = Math.min(newWidth / imageWidth, newHeight / imageHeight);
        const offsetX = (containerWidth - newWidth) / 2;
        const offsetY = (containerHeight - newHeight) / 2;

        // Apply canvas dimensions
        canvas.setDimensions({
          width: containerWidth,
          height: containerHeight,
        });

        // Apply viewport transform to scale and center the image
        canvas.setViewportTransform([scale, 0, 0, scale, offsetX, offsetY]);

        // Single render call
        canvas.renderAll();
      });
    },
    [fabricCanvasRef, containerSelector, isInitialized]
  );

  useEffect(() => {
    if (!isInitialized) return;

    const container = document.querySelector(containerSelector);
    if (!container) return;
    resizeObserverRef.current = new ResizeObserver((entries) => {
      handleResize(entries);
    });

    resizeObserverRef.current.observe(container);

    handleResize();

    return () => {
      // Cleanup ResizeObserver
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
        resizeObserverRef.current = null;
      }
      // Cancel any pending animation frame
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
        rafIdRef.current = null;
      }
    };
  }, [handleResize, containerSelector, isInitialized]);

  // Return manual trigger function
  return {
    triggerResize: () => {
      handleResize();
    },
  };
}
